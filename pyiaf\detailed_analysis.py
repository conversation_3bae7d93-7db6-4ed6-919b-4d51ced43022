import os
import csv
import math
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from scipy import stats

def analyze_experiment_results(folder_path, output_dir=None):
    """
    对实验结果进行详细分析，包括统计信息和可视化
    
    Args:
        folder_path (str): 包含实验结果CSV文件的文件夹路径
        output_dir (str): 输出目录，如果为None则使用folder_path
    """
    if output_dir is None:
        output_dir = folder_path
    
    # 获取所有CSV文件
    all_csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv') and 'client_evaluations' in f]
    if not all_csv_files:
        print(f"在文件夹 '{folder_path}' 中没有找到实验结果CSV文件。")
        return
    
    print(f"找到 {len(all_csv_files)} 个实验结果文件进行分析...")
    
    num_clients = 18
    num_runs = len(all_csv_files)
    
    # 存储所有运行的最优值
    all_optimal_values = []
    convergence_data = []  # 存储收敛曲线数据
    
    for i, filename in enumerate(sorted(all_csv_files)):
        filepath = os.path.join(folder_path, filename)
        print(f"正在分析文件 {i+1}/{num_runs}: {filename}")
        
        try:
            # 读取CSV文件
            df = pd.read_csv(filepath)
            
            # 获取客户端列（排除第一列轮数）
            client_columns = [col for col in df.columns if col.startswith('Client_')]
            
            # 计算每个客户端在该运行中的最优值
            run_optimal_values = []
            for col in client_columns:
                optimal_value = df[col].min()
                run_optimal_values.append(optimal_value)
            
            all_optimal_values.append(run_optimal_values)
            
            # 存储收敛曲线数据（每轮的全局最优值）
            global_best_per_round = []
            for _, row in df.iterrows():
                round_values = [row[col] for col in client_columns]
                global_best_per_round.append(min(round_values))
            convergence_data.append(global_best_per_round)
            
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {e}")
            continue
    
    # 转换为numpy数组便于分析
    all_optimal_values = np.array(all_optimal_values)  # shape: (num_runs, num_clients)
    convergence_data = np.array(convergence_data)      # shape: (num_runs, num_rounds)
    
    # 计算统计信息
    print("\n=== 统计分析结果 ===")
    
    # 每个客户端的统计信息
    client_stats = {}
    for i in range(num_clients):
        client_values = all_optimal_values[:, i]
        client_stats[f'client{i}'] = {
            'mean': np.mean(client_values),
            'std': np.std(client_values),
            'min': np.min(client_values),
            'max': np.max(client_values),
            'median': np.median(client_values),
            'q25': np.percentile(client_values, 25),
            'q75': np.percentile(client_values, 75)
        }
    
    # 保存详细统计信息到CSV
    stats_file = os.path.join(output_dir, 'detailed_statistics.csv')
    with open(stats_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['Client', 'Mean', 'Std', 'Min', 'Max', 'Median', 'Q25', 'Q75'])
        for client, stats_dict in client_stats.items():
            writer.writerow([
                client, 
                f"{stats_dict['mean']:.6f}",
                f"{stats_dict['std']:.6f}",
                f"{stats_dict['min']:.6f}",
                f"{stats_dict['max']:.6f}",
                f"{stats_dict['median']:.6f}",
                f"{stats_dict['q25']:.6f}",
                f"{stats_dict['q75']:.6f}"
            ])
    
    print(f"详细统计信息已保存到: {stats_file}")
    
    # 全局统计
    all_best_values = np.min(all_optimal_values, axis=1)  # 每次运行的全局最优值
    global_stats = {
        'mean': np.mean(all_best_values),
        'std': np.std(all_best_values),
        'min': np.min(all_best_values),
        'max': np.max(all_best_values),
        'median': np.median(all_best_values)
    }
    
    print(f"\n全局最优值统计 (跨 {num_runs} 次运行):")
    print(f"  平均值: {global_stats['mean']:.6f}")
    print(f"  标准差: {global_stats['std']:.6f}")
    print(f"  最小值: {global_stats['min']:.6f}")
    print(f"  最大值: {global_stats['max']:.6f}")
    print(f"  中位数: {global_stats['median']:.6f}")
    
    # 创建可视化图表
    create_visualizations(all_optimal_values, convergence_data, client_stats, global_stats, output_dir)
    
    return client_stats, global_stats, all_optimal_values, convergence_data

def create_visualizations(all_optimal_values, convergence_data, client_stats, global_stats, output_dir):
    """
    创建可视化图表
    """
    print("\n正在生成可视化图表...")
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 1. 客户端性能箱线图
    plt.figure(figsize=(15, 8))
    plt.boxplot([all_optimal_values[:, i] for i in range(18)], 
                labels=[f'Client{i}' for i in range(18)])
    plt.yscale('log')
    plt.title('各客户端最优适应度值分布 (对数尺度)', fontsize=14)
    plt.xlabel('客户端', fontsize=12)
    plt.ylabel('最优适应度值 (对数尺度)', fontsize=12)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'client_performance_boxplot.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 收敛曲线图
    plt.figure(figsize=(12, 8))
    
    # 计算平均收敛曲线和置信区间
    mean_convergence = np.mean(convergence_data, axis=0)
    std_convergence = np.std(convergence_data, axis=0)
    rounds = range(len(mean_convergence))
    
    plt.plot(rounds, mean_convergence, 'b-', linewidth=2, label='平均收敛曲线')
    plt.fill_between(rounds, 
                     mean_convergence - std_convergence, 
                     mean_convergence + std_convergence, 
                     alpha=0.3, color='blue', label='±1标准差')
    
    # 绘制几条典型的收敛曲线
    for i in range(min(5, len(convergence_data))):
        plt.plot(rounds, convergence_data[i], '--', alpha=0.5, linewidth=1)
    
    plt.yscale('log')
    plt.title('算法收敛曲线', fontsize=14)
    plt.xlabel('评估轮数', fontsize=12)
    plt.ylabel('全局最优适应度值 (对数尺度)', fontsize=12)
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'convergence_curves.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 客户端性能排名
    client_means = [client_stats[f'client{i}']['mean'] for i in range(18)]
    sorted_indices = np.argsort(client_means)
    
    plt.figure(figsize=(12, 8))
    plt.bar(range(18), [client_means[i] for i in sorted_indices])
    plt.yscale('log')
    plt.title('客户端平均性能排名', fontsize=14)
    plt.xlabel('客户端 (按性能排序)', fontsize=12)
    plt.ylabel('平均最优适应度值 (对数尺度)', fontsize=12)
    plt.xticks(range(18), [f'Client{i}' for i in sorted_indices], rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'client_ranking.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"可视化图表已保存到: {output_dir}")
    print("  - client_performance_boxplot.png: 客户端性能箱线图")
    print("  - convergence_curves.png: 算法收敛曲线")
    print("  - client_ranking.png: 客户端性能排名")

if __name__ == "__main__":
    # 分析10D_init50_110FE实验结果
    target_folder = os.path.join('pyiaf', '10D_init50_110FE')
    
    if os.path.isdir(target_folder):
        client_stats, global_stats, all_optimal_values, convergence_data = analyze_experiment_results(target_folder)
        print("\n=== 分析完成 ===")
    else:
        print(f"错误：找不到目标文件夹 '{target_folder}'。")
