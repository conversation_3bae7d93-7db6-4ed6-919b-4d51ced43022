#!/usr/bin/env python3
"""
通用的算法结果转换脚本
将CSV格式的实验结果转换为pyfmto可识别的msgpack格式
支持DLSE、FMTBO、FMDLES、FMDLEST等算法
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
import msgpack
from typing import Dict, List, Any
import re
import argparse


def _encode_hook(obj: Any) -> dict:
    """msgpack编码钩子函数"""
    if isinstance(obj, np.ndarray):
        return {
            "__ndarray__": True,
            "dtype": obj.dtype.name,
            "shape": obj.shape,
            "data": obj.tobytes()
        }
    elif isinstance(obj, set):
        return {
            "__set__": True,
            "items": list(obj)
        }
    else:
        raise TypeError(f"Unsupported type: {type(obj)}")


def save_msgpack(data: dict, filename: Path) -> None:
    """保存数据为msgpack格式"""
    with open(filename, 'wb') as f:
        packed = msgpack.packb(data, default=_encode_hook, use_bin_type=True)
        f.write(packed)


def parse_filename(filename: str, algorithm: str) -> Dict[str, Any]:
    """解析运行文件名，提取参数信息"""
    # 通用的运行ID提取
    run_match = re.search(r'run(\d+)', filename)
    run_id = int(run_match.group(1)) if run_match else 0
    
    # 根据算法类型解析不同的参数模式
    if algorithm.upper() == 'DLSE':
        # 示例: run0_DMT_9.18_ktp0.5_FE1_dles1.0_iid_fedavg.csv
        pattern = r'run(\d+)_DMT_([\d.]+)_ktp([\d.]+)_FE(\d+)_dles([\d.]+)_(\w+)_(\w+)\.csv'
        match = re.match(pattern, filename)
        if match:
            return {
                'run_id': int(match.group(1)),
                'dmt': float(match.group(2)),
                'ktp': float(match.group(3)),
                'fe': int(match.group(4)),
                'dles': float(match.group(5)),
                'data_type': match.group(6),
                'aggregation': match.group(7)
            }
    
    elif algorithm.upper() == 'FMTBO':
        # 示例: run0_DMT_18.18_ktp0.8_FE100_EI0.5_iid_fedavg.csv
        pattern = r'run(\d+)_DMT_([\d.]+)_ktp([\d.]+)_FE(\d+)_EI([\d.]+)_(\w+)_(\w+)\.csv'
        match = re.match(pattern, filename)
        if match:
            return {
                'run_id': int(match.group(1)),
                'dmt': float(match.group(2)),
                'ktp': float(match.group(3)),
                'fe': int(match.group(4)),
                'ei': float(match.group(5)),
                'data_type': match.group(6),
                'aggregation': match.group(7)
            }
    
    elif algorithm.upper() == 'FMDLES':
        # 示例: run0_DMT_5.18_ktp0.3_FE1_dles1.0_iid_fedavg.csv
        pattern = r'run(\d+)_DMT_([\d.]+)_ktp([\d.]+)_FE(\d+)_dles([\d.]+)_(\w+)_(\w+)\.csv'
        match = re.match(pattern, filename)
        if match:
            return {
                'run_id': int(match.group(1)),
                'dmt': float(match.group(2)),
                'ktp': float(match.group(3)),
                'fe': int(match.group(4)),
                'dles': float(match.group(5)),
                'data_type': match.group(6),
                'aggregation': match.group(7)
            }

    elif algorithm.upper() == 'FMDLEST':
        # 示例: run0_MT_DLES.csv
        pattern = r'run(\d+)_MT_DLES\.csv'
        match = re.match(pattern, filename)
        if match:
            return {
                'run_id': int(match.group(1)),
                'algorithm': 'FMDLEST',
                'data_type': 'unknown',  # 将从目录名推断
                'aggregation': 'fedavg'
            }
    
    # 如果无法解析，返回默认值
    return {
        'run_id': run_id,
        'algorithm': algorithm,
        'data_type': 'unknown',
        'aggregation': 'fedavg'
    }


def convert_csv_to_solution_format(csv_file: Path, run_info: Dict, algorithm: str) -> Dict[str, Any]:
    """将CSV文件转换为pyfmto Solution格式"""
    df = pd.read_csv(csv_file)
    
    # 获取客户端数量和迭代次数
    num_clients = len(df.columns) - 2  # 减去索引列和时间列
    num_iterations = len(df)
    
    # 创建解决方案字典
    solutions = {}
    
    for client_idx in range(num_clients):
        client_name = f'client{client_idx}'
        if client_name in df.columns:
            # 获取该客户端的适应度历史
            fitness_history = df[client_name].values
            
            # 找到最优值和对应的位置
            best_fitness = np.min(fitness_history)
            best_iteration = np.argmin(fitness_history)
            
            # 创建符合pyfmto Solution类的数据结构
            dim = 10  # 假设10维问题
            
            # 生成模拟的x和y数据
            x_data = np.random.uniform(0, 1, (num_iterations, dim))
            y_data = fitness_history.reshape(-1, 1)
            
            # 客户端ID从1开始（而不是0）
            client_id = client_idx + 1
            
            # 创建Solution格式的数据
            solution_data = {
                '_dim': dim,
                '_obj': 1,
                '_fe_init': num_iterations,
                '_x': x_data,
                '_y': y_data,
                '_prev_size': 0,
                'fitness_history': fitness_history.tolist(),
                'best_fitness': float(best_fitness),
                'best_iteration': int(best_iteration),
                'client_id': client_id,
                'run_info': run_info,
                'algorithm': algorithm
            }
            
            solutions[client_id] = solution_data
    
    return solutions


def convert_algorithm_directory(algorithm: str, source_dir: Path, target_dir: Path):
    """转换指定算法的整个目录结果"""
    print(f"转换{algorithm}目录: {source_dir} -> {target_dir}")

    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)

    # 查找所有运行文件
    run_files = list(source_dir.glob("run*.csv"))
    run_files = [f for f in run_files if not f.name.startswith('average_') and not f.name.startswith('analysis_')]

    print(f"找到 {len(run_files)} 个{algorithm}运行文件")

    # 从目录名推断数据类型（用于FMDLEST）
    data_type_from_dir = source_dir.name.lower()
    if data_type_from_dir == 'iid':
        data_type_from_dir = 'iid'
    elif data_type_from_dir.startswith('niid'):
        data_type_from_dir = data_type_from_dir.lower()

    for run_file in sorted(run_files):
        print(f"处理{algorithm}文件: {run_file.name}")

        # 解析文件名
        run_info = parse_filename(run_file.name, algorithm)
        run_id = run_info['run_id']

        # 对于FMDLEST，从目录名设置数据类型
        if algorithm.upper() == 'FMDLEST' and run_info.get('data_type') == 'unknown':
            run_info['data_type'] = data_type_from_dir

        try:
            # 转换CSV到Solution格式
            solutions = convert_csv_to_solution_format(run_file, run_info, algorithm)

            # 创建RunSolutions格式的数据
            run_solutions_data = {
                '_solutions': solutions
            }

            # 保存为msgpack文件
            target_file = target_dir / f"Run {run_id + 1}.msgpack"
            save_msgpack(run_solutions_data, target_file)
            print(f"  -> 保存到: {target_file}")

        except Exception as e:
            print(f"  错误: {e}")
            continue


def parse_iaf_filename(filename: str) -> Dict[str, Any]:
    """解析IAF格式的文件名，提取运行ID"""
    # 示例: client_evaluations_18clients_10d_110evals_run01.csv
    pattern = r'client_evaluations_(\d+)clients_(\d+)d_(\d+)evals_run(\d+)\.csv'
    match = re.match(pattern, filename)
    if match:
        return {
            'num_clients': int(match.group(1)),
            'dimensions': int(match.group(2)),
            'evaluations': int(match.group(3)),
            'run_id': int(match.group(4)) - 1  # 转换为0-based索引
        }

    # 如果无法解析，尝试提取run号
    run_match = re.search(r'run(\d+)', filename)
    run_id = int(run_match.group(1)) - 1 if run_match else 0

    return {
        'num_clients': 18,  # 默认值
        'dimensions': 10,   # 默认值
        'evaluations': 110, # 默认值
        'run_id': run_id
    }


def convert_iaf_csv_to_solution_format(csv_file: Path, run_info: Dict) -> Dict[str, Any]:
    """将IAF格式的CSV文件转换为pyfmto Solution格式"""
    df = pd.read_csv(csv_file)

    # 获取客户端数量和迭代次数
    client_columns = [col for col in df.columns if col.startswith('Client_')]
    num_clients = len(client_columns)
    num_iterations = len(df)

    print(f"  发现 {num_clients} 个客户端，{num_iterations} 次迭代")

    # 创建解决方案字典
    solutions = {}

    for i, client_col in enumerate(client_columns):
        # 获取该客户端的适应度历史
        fitness_history = df[client_col].values

        # 找到最优值和对应的位置
        best_fitness = np.min(fitness_history)
        best_iteration = np.argmin(fitness_history)

        # 创建符合pyfmto Solution类的数据结构
        dim = run_info.get('dimensions', 10)

        # 生成模拟的x和y数据
        x_data = np.random.uniform(0, 1, (num_iterations, dim))
        y_data = fitness_history.reshape(-1, 1)

        # 客户端ID从1开始（而不是0）
        client_id = i + 1

        # 创建Solution格式的数据
        solution_data = {
            '_dim': dim,
            '_obj': 1,
            '_fe_init': num_iterations,
            '_x': x_data,
            '_y': y_data,
            '_prev_size': 0,
            'fitness_history': fitness_history.tolist(),
            'best_fitness': float(best_fitness),
            'best_iteration': int(best_iteration),
            'client_id': client_id,
            'run_info': run_info,
            'algorithm': 'IAF-FBO'
        }

        solutions[str(client_id)] = solution_data

    return solutions


def convert_iaf_directory(source_dir: Path, target_dir: Path):
    """转换IAF目录的所有CSV文件为msgpack格式"""
    print(f"转换IAF目录: {source_dir} -> {target_dir}")

    # 确保目标目录存在
    target_dir.mkdir(parents=True, exist_ok=True)

    # 查找所有客户端评估文件，排除average_optimal_fitness_corrected.csv
    csv_files = list(source_dir.glob("client_evaluations_*.csv"))

    # 排除不需要转换的文件
    excluded_files = ['average_optimal_fitness_corrected.csv']
    csv_files = [f for f in csv_files if f.name not in excluded_files]

    print(f"找到 {len(csv_files)} 个IAF运行文件")

    for csv_file in sorted(csv_files):
        print(f"处理文件: {csv_file.name}")

        # 解析文件名
        run_info = parse_iaf_filename(csv_file.name)
        run_id = run_info['run_id']

        try:
            # 转换CSV到Solution格式
            solutions = convert_iaf_csv_to_solution_format(csv_file, run_info)

            # 创建RunSolutions格式的数据
            run_solutions_data = {
                '_solutions': solutions
            }

            # 保存为msgpack文件
            target_file = target_dir / f"Run {run_id + 1}.msgpack"
            save_msgpack(run_solutions_data, target_file)
            print(f"  -> 保存到: {target_file}")

        except Exception as e:
            print(f"  错误: {e}")
            continue


def convert_algorithm_results(algorithm: str):
    """转换指定算法的所有结果"""
    if algorithm.upper() == 'IAF' or algorithm.upper() == 'IAF-FBO':
        # IAF-FBO的特殊处理
        source_dir = Path("10D_init50_110FE")
        target_dir = Path("10D_init50_110FE_msgpack")

        if not source_dir.exists():
            print(f"错误: IAF源目录不存在: {source_dir}")
            return

        print(f"\n开始转换IAF-FBO算法结果...")
        convert_iaf_directory(source_dir, target_dir)
        print(f"\nIAF-FBO转换完成！")
        print(f"转换后的结果保存在: {target_dir}")
        return

    if algorithm.upper() == 'FMDLEST':
        # FMDLEST有特殊的目录结构
        source_root = Path(f"out/results/{algorithm}")
        target_root = Path(f"out/results/{algorithm}/ARXIV2017_10D")

        if not source_root.exists():
            print(f"错误: {algorithm}源目录不存在: {source_root}")
            return

        print(f"\n开始转换{algorithm}算法结果...")

        # FMDLEST的数据类型目录名
        data_types = ['IID', 'NIID2', 'NIID4', 'NIID6']

        for data_type in data_types:
            source_dir = source_root / data_type
            target_dir = target_root / data_type.upper()  # 保持大写

            if source_dir.exists():
                convert_algorithm_directory(algorithm, source_dir, target_dir)
            else:
                print(f"跳过不存在的目录: {source_dir}")
    else:
        # 其他算法的标准目录结构
        source_root = Path(f"out/results/{algorithm}/ARXIV2017_10D")
        target_root = Path(f"out/results/{algorithm}/ARXIV2017_10D")

        if not source_root.exists():
            print(f"错误: {algorithm}源目录不存在: {source_root}")
            return

        print(f"\n开始转换{algorithm}算法结果...")

        # 处理所有数据类型目录
        data_types = ['iid', 'niid2', 'niid4', 'niid6']

        for data_type in data_types:
            source_dir = source_root / data_type
            target_dir = target_root / data_type.upper()  # 转换为大写以保持一致性

            if source_dir.exists():
                convert_algorithm_directory(algorithm, source_dir, target_dir)
            else:
                print(f"跳过不存在的目录: {source_dir}")

    print(f"\n{algorithm}转换完成！")
    print(f"转换后的结果保存在: {target_root if algorithm.upper() == 'FMDLEST' else target_root}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='通用算法结果转换工具')
    parser.add_argument('algorithm', help='算法名称 (DLSE, FMTBO, FMDLES, FMDLEST, IAF等)')
    parser.add_argument('--all', action='store_true', help='转换所有支持的算法')

    args = parser.parse_args()

    if args.all:
        # 转换所有算法
        algorithms = ['DLSE', 'FMTBO', 'FMDLES', 'FMDLEST', 'IAF']
        for alg in algorithms:
            if alg.upper() == 'IAF':
                if Path("10D_init50_110FE").exists():
                    convert_algorithm_results(alg)
                else:
                    print(f"跳过不存在的算法: {alg}")
            elif Path(f"out/results/{alg}").exists():
                convert_algorithm_results(alg)
            else:
                print(f"跳过不存在的算法: {alg}")
    else:
        # 转换指定算法
        convert_algorithm_results(args.algorithm.upper())

    print(f"\n{'='*60}")
    print("转换完成！")
    print("客户端ID已设置为1-18")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
