"""
Neural Network Classifier for pairwise comparisons in IAF-FBO algorithm
"""

import numpy as np
from sklearn.neural_network import MLPClassifier
from sklearn.model_selection import train_test_split

# 修复相对导入问题
try:
    from .utils import one_hot_encode, one_hot_decode, create_pairwise_data
except ImportError:
    from utils import one_hot_encode, one_hot_decode, create_pairwise_data
import warnings
warnings.filterwarnings('ignore')


class NeuralClassifier:
    """
    Neural Network Classifier for pairwise preference learning
    """
    
    def __init__(self, hidden_layers=None, max_iter=1000, learning_rate_init=0.001,
                 random_state=None, early_stopping=True, validation_fraction=0.1):
        """
        Initialize Neural Classifier
        
        Args:
            hidden_layers: Tuple of hidden layer sizes
            max_iter: Maximum number of iterations
            learning_rate_init: Initial learning rate
            random_state: Random seed
            early_stopping: Whether to use early stopping
            validation_fraction: Fraction of data for validation
        """
        self.hidden_layers = hidden_layers
        self.max_iter = max_iter
        self.learning_rate_init = learning_rate_init
        self.random_state = random_state
        self.early_stopping = early_stopping
        self.validation_fraction = validation_fraction
        
        self.model = None
        self.input_dim = None
        self.is_fitted = False
        
    def fit(self, X, y, sample_weight=None):
        """
        Fit the neural classifier
        
        Args:
            X: Input features (pairwise concatenated)
            y: Target labels (-1, 0, 1)
            sample_weight: Sample weights
        """
        X = np.asarray(X)
        y = np.asarray(y)
        
        if X.ndim != 2:
            raise ValueError("X must be 2D array")
        
        self.input_dim = X.shape[1]
        
        # Set default hidden layers based on input dimension
        if self.hidden_layers is None:
            dim = self.input_dim // 2  # Since input is pairwise concatenated
            self.hidden_layers = (int(dim * 1.5), dim, int(dim / 2))
        
        # Convert labels to binary classification problem
        # We'll use a multi-class approach: class 0 for label 1, class 1 for label -1, class 2 for label 0
        y_multiclass = np.zeros_like(y, dtype=int)
        y_multiclass[y == 1] = 0
        y_multiclass[y == -1] = 1
        y_multiclass[y == 0] = 2
        
        # Create and train the model
        self.model = MLPClassifier(
            hidden_layer_sizes=self.hidden_layers,
            max_iter=self.max_iter,
            learning_rate_init=self.learning_rate_init,
            random_state=self.random_state,
            early_stopping=self.early_stopping,
            validation_fraction=self.validation_fraction,
            solver='adam',
            activation='relu'
        )
        
        try:
            if sample_weight is not None:
                self.model.fit(X, y_multiclass, sample_weight=sample_weight)
            else:
                self.model.fit(X, y_multiclass)
            self.is_fitted = True
        except Exception as e:
            print(f"Warning: Neural network training failed: {e}")
            # Fallback to simpler model
            self.model = MLPClassifier(
                hidden_layer_sizes=(50,),
                max_iter=500,
                random_state=self.random_state,
                solver='lbfgs'
            )
            try:
                if sample_weight is not None:
                    self.model.fit(X, y_multiclass, sample_weight=sample_weight)
                else:
                    self.model.fit(X, y_multiclass)
            except:
                # Final fallback - ignore sample weights
                self.model.fit(X, y_multiclass)
            self.is_fitted = True
    
    def predict(self, X):
        """
        Make predictions
        
        Args:
            X: Input features
        
        Returns:
            Predicted labels (-1, 0, 1)
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        X = np.asarray(X)
        if X.ndim == 1:
            X = X.reshape(1, -1)
        
        # Get predictions from the model
        y_pred_multiclass = self.model.predict(X)
        
        # Convert back to original labels
        y_pred = np.zeros_like(y_pred_multiclass)
        y_pred[y_pred_multiclass == 0] = 1
        y_pred[y_pred_multiclass == 1] = -1
        y_pred[y_pred_multiclass == 2] = 0
        
        return y_pred
    
    def predict_proba(self, X):
        """
        Predict class probabilities
        
        Args:
            X: Input features
        
        Returns:
            Class probabilities
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        X = np.asarray(X)
        if X.ndim == 1:
            X = X.reshape(1, -1)
        
        return self.model.predict_proba(X)
    
    def get_weights(self):
        """
        Get model weights as a flattened vector

        Returns:
            Flattened weight vector
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before extracting weights")

        weights = []

        try:
            # Get weights from all layers
            for coef in self.model.coefs_:
                weights.append(coef.flatten())

            # Get biases from all layers
            for intercept in self.model.intercepts_:
                weights.append(intercept.flatten())

            if weights:
                return np.concatenate(weights)
            else:
                # Return a default weight vector if no weights found
                return np.random.randn(100)
        except Exception as e:
            print(f"Warning: Could not extract weights: {e}")
            # Return a default weight vector
            return np.random.randn(100)
    
    def set_weights(self, weight_vector):
        """
        Set model weights from a flattened vector

        Args:
            weight_vector: Flattened weight vector
        """
        if not self.is_fitted:
            raise ValueError("Model must be fitted before setting weights")

        # Reconstruct weights and biases
        idx = 0

        try:
            # Set weights for all layers
            for i, coef in enumerate(self.model.coefs_):
                size = coef.size
                if idx + size <= len(weight_vector):
                    self.model.coefs_[i] = weight_vector[idx:idx+size].reshape(coef.shape)
                    idx += size
                else:
                    break

            # Set biases for all layers
            for i, intercept in enumerate(self.model.intercepts_):
                size = intercept.size
                if idx + size <= len(weight_vector):
                    self.model.intercepts_[i] = weight_vector[idx:idx+size].reshape(intercept.shape)
                    idx += size
                else:
                    break
        except Exception as e:
            print(f"Warning: Could not set weights: {e}")
            # Keep original weights if setting fails
    
    def copy(self):
        """
        Create a copy of the classifier
        
        Returns:
            Copy of the classifier
        """
        new_classifier = NeuralClassifier(
            hidden_layers=self.hidden_layers,
            max_iter=self.max_iter,
            learning_rate_init=self.learning_rate_init,
            random_state=self.random_state,
            early_stopping=self.early_stopping,
            validation_fraction=self.validation_fraction
        )
        
        if self.is_fitted:
            # Create a dummy model with same structure
            dummy_X = np.random.randn(10, self.input_dim)
            dummy_y = np.random.randint(0, 3, 10)
            new_classifier.model = MLPClassifier(
                hidden_layer_sizes=self.hidden_layers,
                max_iter=1,
                random_state=self.random_state
            )
            new_classifier.model.fit(dummy_X, dummy_y)
            new_classifier.input_dim = self.input_dim
            new_classifier.is_fitted = True
            
            # Copy weights
            new_classifier.set_weights(self.get_weights())
        
        return new_classifier


def aggregate_classifiers(classifiers, client_ids):
    """
    Aggregate multiple neural classifiers by averaging their weights

    Args:
        classifiers: List of NeuralClassifier objects
        client_ids: List of client IDs to aggregate

    Returns:
        Aggregated classifier
    """
    if not client_ids:
        raise ValueError("client_ids cannot be empty")

    # Filter out unfitted classifiers
    fitted_client_ids = []
    for client_id in client_ids:
        if classifiers[client_id].is_fitted:
            fitted_client_ids.append(client_id)

    if not fitted_client_ids:
        raise ValueError("No fitted classifiers found")

    # Get weight vectors from all fitted classifiers
    weight_vectors = []
    for client_id in fitted_client_ids:
        weight_vector = classifiers[client_id].get_weights()
        weight_vectors.append(weight_vector)

    # Check if all weight vectors have the same size
    weight_sizes = [len(w) for w in weight_vectors]

    if len(set(weight_sizes)) == 1:
        # All weights have the same size - use simple averaging
        weight_sum = np.zeros_like(weight_vectors[0])
        for weight_vector in weight_vectors:
            weight_sum += weight_vector
        avg_weights = weight_sum / len(weight_vectors)

        # Use the first classifier as template
        agg_classifier = classifiers[fitted_client_ids[0]].copy()
        agg_classifier.set_weights(avg_weights)

    else:
        # Different weight sizes - use the most common size as template
        from collections import Counter
        size_counts = Counter(weight_sizes)
        most_common_size = size_counts.most_common(1)[0][0]

        # Find classifiers with the most common weight size
        compatible_classifiers = []
        for i, client_id in enumerate(fitted_client_ids):
            if weight_sizes[i] == most_common_size:
                compatible_classifiers.append(client_id)

        if not compatible_classifiers:
            # Fallback: use the first classifier
            agg_classifier = classifiers[fitted_client_ids[0]].copy()
        else:
            # Average weights from compatible classifiers
            weight_sum = np.zeros(most_common_size)
            for client_id in compatible_classifiers:
                weight_sum += classifiers[client_id].get_weights()
            avg_weights = weight_sum / len(compatible_classifiers)

            # Use the first compatible classifier as template
            agg_classifier = classifiers[compatible_classifiers[0]].copy()
            agg_classifier.set_weights(avg_weights)

    return agg_classifier


def train_pairwise_classifier(X_data, y_af, bounds, noise_prob=0.0, random_state=None):
    """
    Train a pairwise classifier from acquisition function data
    
    Args:
        X_data: Input data points
        y_af: Acquisition function values
        bounds: Problem bounds for normalization
        noise_prob: Probability of label noise
        random_state: Random seed
    
    Returns:
        Trained classifier
    """
    # Normalize input data
    X_norm = (X_data - bounds[0]) / (bounds[1] - bounds[0])
    
    # Create pairwise comparison data
    X_pairs, y_pairs = create_pairwise_data(X_norm, y_af)
    
    # Add label noise if specified
    if noise_prob > 0 and random_state is not None:
        np.random.seed(random_state)
        noise_mask = np.random.random(len(y_pairs)) < noise_prob
        y_pairs[noise_mask] *= -1  # Flip labels
    
    # Train classifier
    classifier = NeuralClassifier(random_state=random_state)
    classifier.fit(X_pairs, y_pairs)
    
    return classifier
